# 五线谱视奏练习程序需求文档

## 项目概述
开发一个面向初学者的五线谱视奏练习程序，帮助用户学习识别音符位置和对应的音名、唱名，并提供交互式钢琴键盘功能。

## 功能需求

### 1. 五线谱显示
- **谱表类型**: 仅显示高音谱号（G谱号）
- **五线谱结构**: 标准五线四间，包含必要的加线
- **音符范围**: 下加二线到上加二线
  - 下加二线: A3
  - 下加一线: C4 (中央C)
  - 第一线: E4
  - 第一间: F4
  - 第二线: G4
  - 第二间: A4
  - 第三线: B4
  - 第三间: C5
  - 第四线: D5
  - 第四间: E5
  - 第五线: F5
  - 上加一线: A5
  - 上加二线: C6

### 2. 练习模式

#### 2.1 视奏练习模式（原有功能）
- **音符类型**: 四分音符
- **显示方式**: 随机在指定音高位置显示单个音符
- **音符外观**: 标准音乐记谱法，黑色符头，符干方向遵循音乐规则
  - 第三线以下：符干向上
  - 第三线以上：符干向下

#### 2.2 钢琴键盘交互模式（新增功能）
- **虚拟钢琴**: 显示对应音符范围的钢琴键盘（A3-C6，共13个键）
- **键盘布局**: 
  - 白键：A3, C4, D4, E4, F4, G4, A4, B4, C5, D5, E5, F5, A5, C6
  - 黑键：A#3, C#4, D#4, F#4, G#4, A#4, C#5, D#5, F#5, G#5, A#5
- **交互功能**:
  - 鼠标点击钢琴键发出对应音高
  - 同时在五线谱上显示对应的音符
  - 支持连续点击，在五线谱上依次显示多个音符

### 3. 音频播放
- **音源**: 钢琴音色或纯音调
- **音高**: 与显示音符对应的准确音高
- **播放时机**: 
  - 视奏模式：音符显示时自动播放
  - 钢琴模式：点击钢琴键时播放

### 4. 音名显示
- **位置**: 五线谱下方、钢琴键盘上方
- **内容**: 
  - 音名：A B C D E F G（按音高顺序）
  - 唱名：la si do re mi fa sol
- **显示方式**: 当前音符对应的音名和唱名高亮显示

### 5. 交互功能

#### 5.1 视奏练习模式
- **下一题**: 点击按钮或按键切换到下一个随机音符
- **重播**: 重新播放当前音符的声音
- **答案显示**: 显示/隐藏当前音符的音名和唱名

#### 5.2 钢琴键盘模式
- **点击播放**: 鼠标点击钢琴键播放音符并在五线谱显示
- **清除五线谱**: 清空五线谱上的所有音符
- **模式切换**: 在视奏练习和钢琴键盘模式间切换

## 技术实现方案

### 1. 开发环境
- **编程语言**: Python
- **图形界面**: Tkinter 或 Pygame
- **音频处理**: pygame.mixer 或 pydub + simpleaudio

### 2. 核心模块设计

#### 2.1 音符数据模型
```python
# 音符信息结构
note_info = {
    'pitch': 'C4',           # 音高标记
    'position': 'ledger_-1', # 在五线谱上的位置
    'frequency': 261.63,     # 频率(Hz)
    'note_name': 'C',        # 音名
    'solfege': 'do',         # 唱名
    'is_black_key': False    # 是否为黑键
}
```

#### 2.2 五线谱绘制模块
- 绘制五线谱基本结构
- 绘制高音谱号
- 绘制加线（根据需要）
- 绘制音符在指定位置
- 支持多个音符同时显示

#### 2.3 钢琴键盘模块（新增）
- 绘制虚拟钢琴键盘
- 白键和黑键的正确布局
- 鼠标点击检测
- 按键按下视觉反馈
- 键盘标签显示（音名）

#### 2.4 音频生成/播放模块
- 生成或加载音频文件
- 播放指定音高的声音
- 支持同时播放多个音符（和弦）

#### 2.5 用户界面模块
- 主窗口布局
- 模式切换控件
- 按钮控件（下一题、重播、显示答案、清除）
- 音名唱名显示区域

### 3. 数据准备

#### 3.1 完整音符位置映射（包含黑键）
| 位置 | 音高 | 频率(Hz) | 音名 | 唱名 | 键盘类型 |
|------|------|----------|------|------|----------|
| 下加二线 | A3 | 220.00 | A | la | 白键 |
| - | A#3 | 233.08 | A# | - | 黑键 |
| 下加一线 | C4 | 261.63 | C | do | 白键 |
| - | C#4 | 277.18 | C# | - | 黑键 |
| - | D4 | 293.66 | D | re | 白键 |
| - | D#4 | 311.13 | D# | - | 黑键 |
| 第一线 | E4 | 329.63 | E | mi | 白键 |
| 第一间 | F4 | 349.23 | F | fa | 白键 |
| - | F#4 | 369.99 | F# | - | 黑键 |
| 第二线 | G4 | 392.00 | G | sol | 白键 |
| - | G#4 | 415.30 | G# | - | 黑键 |
| 第二间 | A4 | 440.00 | A | la | 白键 |
| - | A#4 | 466.16 | A# | - | 黑键 |
| 第三线 | B4 | 493.88 | B | si | 白键 |
| 第三间 | C5 | 523.25 | C | do | 白键 |
| - | C#5 | 554.37 | C# | - | 黑键 |
| 第四线 | D5 | 587.33 | D | re | 白键 |
| - | D#5 | 622.25 | D# | - | 黑键 |
| 第四间 | E5 | 659.25 | E | mi | 白键 |
| 第五线 | F5 | 698.46 | F | fa | 白键 |
| - | F#5 | 739.99 | F# | - | 黑键 |
| - | G5 | 783.99 | G | sol | 白键 |
| - | G#5 | 830.61 | G# | - | 黑键 |
| 上加一线 | A5 | 880.00 | A | la | 白键 |
| - | A#5 | 932.33 | A# | - | 黑键 |
| - | B5 | 987.77 | B | si | 白键 |
| 上加二线 | C6 | 1046.50 | C | do | 白键 |

#### 3.2 音频文件
- 准备完整的音频文件（包含黑键，约25个音符）
- 或使用程序生成纯音调

## 用户体验设计

### 1. 界面布局（更新版）
```
┌─────────────────────────────────────────────────────────┐
│                五线谱视奏练习                           │
├─────────────────────────────────────────────────────────┤
│ [视奏练习] [钢琴键盘] 模式                              │
├─────────────────────────────────────────────────────────┤
│                                                         │
│            [高音谱号] ♪ ♪ ♪                            │
│         ─────────────────────────                       │
│         ─────────────────────────                       │
│         ─────────────────────────                       │
│         ─────────────────────────                       │
│         ─────────────────────────                       │
│                                                         │
├─────────────────────────────────────────────────────────┤
│      音名: A B [C] D E F G                              │
│      唱名: la si [do] re mi fa sol                      │
├─────────────────────────────────────────────────────────┤
│  [下一题] [重播] [显示答案] [清除五线谱]                │
├─────────────────────────────────────────────────────────┤
│                    虚拟钢琴键盘                         │
│  ┌─┐ ┌─┐   ┌─┐ ┌─┐ ┌─┐   ┌─┐ ┌─┐   ┌─┐ ┌─┐ ┌─┐       │
│  │#│ │#│   │#│ │#│ │#│   │#│ │#│   │#│ │#│ │#│       │
│  │ │ │ │   │ │ │ │ │ │   │ │ │ │   │ │ │ │ │ │       │
│ ┌┴┐┌┴┐┌─┐┌┴┐┌┴┐┌┴┐┌─┐┌┴┐┌┴┐┌─┐┌┴┐┌┴┐┌┴┐┌─┐┌─┐       │
│ │A││ ││C││ │ │ ││E││F││ │ │ ││A││ ││C││ │ │ ││F││A│     │
│ │3││ ││4││ │ │ ││4││4││ │ │ ││4││ ││5││ │ │ ││5││5│     │
│ └─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘└─┘       │
└─────────────────────────────────────────────────────────┘
```

### 2. 操作流程

#### 2.1 视奏练习模式
1. 选择"视奏练习"模式
2. 点击"下一题"，随机显示音符并播放声音
3. 用户观察音符位置，思考对应的音名和唱名
4. 可点击"重播"重新听音
5. 可点击"显示答案"查看正确答案

#### 2.2 钢琴键盘模式
1. 选择"钢琴键盘"模式
2. 点击虚拟钢琴键，听到对应音高
3. 同时在五线谱上看到对应音符出现
4. 可连续点击多个键，在五线谱上显示音符序列
5. 点击"清除五线谱"重新开始

### 3. 学习辅助功能
- **渐进式学习**: 可选择音符范围（如先练习五线内音符）
- **统计功能**: 记录练习次数和正确率
- **错误提示**: 标记容易混淆的音符位置
- **双向学习**: 从五线谱到键盘，从键盘到五线谱

## 扩展功能（后期开发）
1. **难度等级**: 不同音符范围的练习模式
2. **节拍练习**: 加入不同时值的音符
3. **和弦识别**: 显示简单和弦，支持多键同时按下
4. **进度跟踪**: 学习进度和成绩统计
5. **自定义设置**: 音色选择、播放速度等
6. **键盘快捷键**: 支持电脑键盘映射到钢琴键
7. **录制回放**: 录制用户弹奏的序列并回放

## 开发优先级
1. **第一阶段**: 基本五线谱显示和单音符识别
2. **第二阶段**: 虚拟钢琴键盘绘制和点击检测
3. **第三阶段**: 钢琴键盘与五线谱的联动功能
4. **第四阶段**: 音频播放和用户交互完善
5. **第五阶段**: 界面美化和用户体验优化
6. **第六阶段**: 扩展功能开发

## 技术难点和解决方案
1. **五线谱精确绘制**: 使用坐标计算确保音符位置准确
2. **钢琴键盘布局**: 正确计算白键黑键的位置和大小关系
3. **鼠标点击检测**: 精确检测点击在哪个钢琴键上
4. **音频同步**: 确保音符显示和声音播放的时机一致
5. **多音符显示**: 在五线谱上管理多个音符的显示和清除
6. **跨平台兼容**: 选择合适的音频库确保在不同系统上正常运行
7. **性能优化**: 音频文件预加载，避免播放延迟

---

*此文档将作为开发指导，在实现过程中可根据实际情况调整和完善。*
